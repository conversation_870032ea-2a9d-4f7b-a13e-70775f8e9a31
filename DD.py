# ==========================
# Import Libraries
# ==========================
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer, KNNImputer

# Handle IterativeImputer import for different sklearn versions
try:
    from sklearn.impute import IterativeImputer
except ImportError:
    IterativeImputer = None

from sklearn.metrics import precision_score, recall_score, f1_score, classification_report
from sklearn.model_selection import train_test_split
from scipy.stats import zscore
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import RFE
from sklearn.metrics import confusion_matrix
from sklearn.metrics import roc_curve, auc

# ==========================
# Streamlit Page Configuration
# ==========================
st.set_page_config(page_title="Laptop Recommendation System",
                   page_icon="💻",
                   layout="wide")

# ==========================
# Load Dataset
# ==========================
@st.cache_data
def load_data():
    try:
        data = pd.read_csv("laptops.csv")
        return data
    except FileNotFoundError:
        st.error("Error: File 'laptops.csv' not found.")
        return pd.DataFrame()

data = load_data()

if data.empty:
    st.stop()

# ==========================
# Main Application Logic
# ==========================
# Set page background and global styles
st.markdown("""
<style>
/* Global Styles */
.main .block-container {
    background-color: #F0EFED;
    padding-top: 2rem;
}

/* Custom CSS for Modern Calm Theme */
.stApp {
    background-color: #F0EFED;
}

/* Improved text contrast */
.stMarkdown {
    color: #32127A;
}

/* Better visibility for metric labels */
.metric-label {
    color: #32127A !important;
    font-weight: 600 !important;
}

/* Fix metric card text visibility */
.stMetric {
    background-color: rgba(255, 255, 255, 0.9) !important;
    padding: 1rem !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1) !important;
}

.stMetric > div {
    color: #32127A !important;
}

.stMetric label {
    color: #32127A !important;
    font-weight: 700 !important;
    font-size: 0.9rem !important;
}

.stMetric [data-testid="metric-container"] {
    background-color: rgba(255, 255, 255, 0.9) !important;
    padding: 1rem !important;
    border-radius: 10px !important;
    border-left: 4px solid #008292 !important;
}

.stMetric [data-testid="metric-container"] > div {
    color: #32127A !important;
}

.stMetric [data-testid="metric-container"] label {
    color: #32127A !important;
    font-weight: 700 !important;
}

/* Force metric text visibility */
.stMetric [data-testid="metric-container"] [data-testid="metric-value"] {
    color: #32127A !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
}

.stMetric [data-testid="metric-container"] [data-testid="metric-delta"] {
    color: #008292 !important;
    font-weight: 600 !important;
}

/* Alternative approach for metric styling */
div[data-testid="metric-container"] {
    background: rgba(255, 255, 255, 0.95) !important;
    padding: 1.2rem !important;
    border-radius: 12px !important;
    border-left: 4px solid #008292 !important;
    box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1) !important;
}

div[data-testid="metric-container"] * {
    color: #32127A !important;
}

/* Enhanced readability for cards */
.modern-card {
    color: #32127A !important;
}

/* Improved contrast for data displays */
.stDataFrame {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(50, 18, 122, 0.1);
}

/* Data Cleaning tab text improvements */
.stSelectbox label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stMultiSelect label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stRadio label {
    color: #32127A !important;
    font-weight: 600 !important;
}

.stSlider label {
    color: #32127A !important;
    font-weight: 600 !important;
}

/* Improve button styling */
.stButton > button {
    background-color: #008292 !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

.stButton > button:hover {
    background-color: #32127A !important;
    color: white !important;
}

/* Tab Styling */
.stTabs [data-baseweb="tab-list"] {
    gap: 8px;
    justify-content: center;
    margin-bottom: 2rem;
    background-color: transparent;
    padding: 1rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%);
    box-shadow: 0 8px 32px rgba(0, 130, 146, 0.1);
}

.stTabs [data-baseweb="tab"] {
    height: 65px;
    padding: 0px 28px;
    background-color: #F0EFED;
    border-radius: 15px;
    color: #32127A;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid #CDE0E1;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 4px;
    box-shadow: 0 4px 12px rgba(50, 18, 122, 0.1);
}

.stTabs [aria-selected="true"] {
    background: linear-gradient(135deg, #008292 0%, #32127A 100%);
    color: white;
    border-color: #008292;
    box-shadow: 0 8px 25px rgba(0, 130, 146, 0.4);
    transform: translateY(-2px);
}

.stTabs [data-baseweb="tab"]:hover {
    background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(179, 68, 108, 0.2);
    border-color: #B3446C;
}

/* Title Styling */
.main-title {
    background: linear-gradient(135deg, #32127A 0%, #008292 50%, #B3446C 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 3.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

.subtitle {
    color: #32127A;
    font-size: 1.3rem;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 500;
    opacity: 0.8;
}

/* Card Styles */
.modern-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 6px 24px rgba(50, 18, 122, 0.08);
    border: 1px solid rgba(227, 204, 220, 0.3);
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
}

/* Button Styles */
.stButton > button {
    background: linear-gradient(135deg, #008292 0%, #B3446C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 130, 146, 0.3);
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(179, 68, 108, 0.4);
    background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
}

/* Metric Cards */
.metric-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(227, 204, 220, 0.3) 100%);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(205, 224, 225, 0.5);
    box-shadow: 0 8px 32px rgba(50, 18, 122, 0.08);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 130, 146, 0.15);
}
</style>
""", unsafe_allow_html=True)

# Modern title with gradient text
st.markdown("""
<div style="text-align: center; margin-bottom: 3rem; padding: 2rem; background: linear-gradient(135deg, rgba(227, 204, 220, 0.3) 0%, rgba(205, 224, 225, 0.3) 100%); border-radius: 25px; backdrop-filter: blur(10px);">
    <h1 class="main-title">💻 Laptop Recommendation System</h1>
    <p class="subtitle">Discover your perfect laptop with our AI-powered recommendation platform</p>
</div>
""", unsafe_allow_html=True)

# Create navigation tabs
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "👋 Welcome",
    "📊 Data Visualization",
    "🧹 Data Cleaning Insights",
    "🤖 Model Evaluation",
    "💻 Laptop Recommendation"
])

with tab1:
    # Hero Section with Modern Calm Theme
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 3rem 2rem;
        border-radius: 25px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 15px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h1 style="font-size: 2.8rem; margin-bottom: 1rem; font-weight: 700; letter-spacing: -0.02em;">👋 Welcome to Your Tech Journey!</h1>
        <p style="font-size: 1.4rem; margin-bottom: 1.5rem; opacity: 0.95; line-height: 1.6;">Find your perfect laptop with our intelligent AI-powered recommendation system</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Overview Section
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        border-left: 5px solid #008292;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(50, 18, 122, 0.1);
    ">
        <h2 style="color: #32127A; margin-bottom: 1.5rem; display: flex; align-items: center; font-weight: 700;">
            <span style="margin-right: 0.5rem;">📊</span> Dataset Overview
        </h2>
        <p style="color: #32127A; opacity: 0.8; font-size: 1.1rem; margin: 0;">Comprehensive laptop data analysis for informed decision-making</p>
    </div>
    """, unsafe_allow_html=True)

    # Dataset Statistics
    if not data.empty:
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            total_laptops = len(data)
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #32127A 0%, #008292 100%);
                color: white;
                transform: scale(1);
                transition: all 0.3s ease;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💻</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{total_laptops:,}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Total Laptops</div>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            unique_brands = data['Brand'].nunique() if 'Brand' in data.columns else 0
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
                color: white;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🏷️</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{unique_brands}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Unique Brands</div>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            if 'Final Price' in data.columns:
                min_price = int(data['Final Price'].min())
                max_price = int(data['Final Price'].max())
                price_range = f"${min_price:,} - ${max_price:,}"
            else:
                price_range = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #008292 0%, #CDE0E1 100%);
                color: #32127A;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💰</div>
                <div style="font-size: 1.2rem; font-weight: bold;">{price_range}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">Price Range</div>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            if 'RAM' in data.columns:
                avg_ram = int(data['RAM'].mean())
                ram_info = f"{avg_ram} GB"
            else:
                ram_info = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #E3CCDC 0%, #B3446C 100%);
                color: white;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">🧠</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{ram_info}</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">Avg RAM</div>
            </div>
            """, unsafe_allow_html=True)

        with col5:
            if 'Storage' in data.columns:
                avg_storage = int(data['Storage'].mean())
                storage_info = f"{avg_storage} GB"
            else:
                storage_info = "N/A"
            st.markdown(f"""
            <div class="metric-card" style="
                background: linear-gradient(135deg, #CDE0E1 0%, #008292 100%);
                color: #32127A;
            ">
                <div style="font-size: 2.2rem; margin-bottom: 0.5rem;">💾</div>
                <div style="font-size: 1.8rem; font-weight: bold;">{storage_info}</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">Avg Storage</div>
            </div>
            """, unsafe_allow_html=True)



    # Features Section
    st.markdown("""
    <div style="margin: 3rem 0 2rem 0;">
        <h3 style="color: #32127A; text-align: center; margin-bottom: 2rem; font-weight: 700; font-size: 2rem;">🚀 Platform Features</h3>
        <p style="color: #32127A; text-align: center; opacity: 0.7; font-size: 1.1rem; margin-bottom: 2rem;">Powerful tools designed for the modern tech community</p>
    </div>
    """, unsafe_allow_html=True)

    # Feature cards with enhanced design
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #008292 0%, #32127A 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">📊</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Visualization</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Explore comprehensive charts and graphs to understand laptop market trends, brand distributions, and feature correlations.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #CDE0E1 0%, #008292 100%);
            color: #32127A;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🤖</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Model Evaluation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Evaluate machine learning models with detailed performance metrics including accuracy, precision, recall, and F1-score.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #B3446C 0%, #32127A 100%);
            color: white;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🧹</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Data Cleaning</h4>
            </div>
            <p style="margin: 0; opacity: 0.95; line-height: 1.6; font-size: 1rem;">Clean and preprocess data with advanced tools for handling missing values, outliers, duplicates, and feature engineering.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="modern-card" style="
            background: linear-gradient(135deg, #E3CCDC 0%, #B3446C 100%);
            color: #32127A;
            margin-bottom: 1rem;
        ">
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">💻</div>
                <h4 style="margin: 0; font-size: 1.4rem; font-weight: 700;">Laptop Recommendation</h4>
            </div>
            <p style="margin: 0; opacity: 0.9; line-height: 1.6; font-size: 1rem;">Get personalized laptop recommendations based on your budget, usage requirements, and preferred specifications.</p>
        </div>
        """, unsafe_allow_html=True)

    # Call to Action
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        margin: 2rem 0;
        color: white;
        box-shadow: 0 12px 40px rgba(50, 18, 122, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    ">
        <h3 style="margin-bottom: 1rem; font-size: 1.8rem; font-weight: 700;">🎆 Ready to Find Your Perfect Laptop?</h3>
        <p style="margin-bottom: 2rem; opacity: 0.95; font-size: 1.2rem; line-height: 1.5;">Navigate through our tabs to explore data insights or jump directly to our recommendation engine!</p>
        <div style="display: flex; justify-content: center; gap: 1.5rem; flex-wrap: wrap;">
            <div style="background: rgba(227, 204, 220, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">📊 Analyze Data</div>
            <div style="background: rgba(205, 224, 225, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🧹 Clean Data</div>
            <div style="background: rgba(179, 68, 108, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">🤖 Evaluate Models</div>
            <div style="background: rgba(0, 130, 146, 0.3); padding: 0.75rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 600; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2);">💻 Get Recommendations</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

with tab2:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">📊 Data Visualization Dashboard</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Comprehensive insights into laptop market trends and specifications</p>
    </div>
    """, unsafe_allow_html=True)

    viz_tab1, viz_tab2, viz_tab3, viz_tab4, viz_tab5, viz_tab6 = st.tabs([
        "🏷️ Brand Insights",
        "💰 Price Analytics",
        "💾 Hardware Specs",
        "🖥️ Display & Design",
        "🔗 Correlations",
        "📈 Market Overview"
    ])

    with viz_tab1:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">🏷️ Brand Market Analysis</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive analysis of laptop brands and their market presence</p>
        </div>
        """, unsafe_allow_html=True)

        # Brand Overview Metrics
        if 'Brand' in data.columns:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_brands = data['Brand'].nunique()
                st.metric("🏷️ Total Brands", total_brands)

            with col2:
                top_brand = data['Brand'].value_counts().index[0]
                top_brand_count = data['Brand'].value_counts().iloc[0]
                st.metric("🏆 Leading Brand", top_brand, f"{top_brand_count} models")

            with col3:
                avg_models_per_brand = round(len(data) / data['Brand'].nunique(), 1)
                st.metric("📊 Avg Models/Brand", avg_models_per_brand)

            with col4:
                if 'Final Price' in data.columns:
                    avg_price_by_brand = data.groupby('Brand')['Final Price'].mean()
                    most_expensive_brand = avg_price_by_brand.idxmax()
                    st.metric("💰 Premium Brand", most_expensive_brand)

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Brand Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Brand' in data.columns:
                # Enhanced donut chart with better styling
                top_brands = data['Brand'].value_counts().head(8)

                # Set matplotlib style for better integration
                plt.style.use('default')
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')  # Match background

                # Use your theme colors
                colors = ['#32127A', '#008292', '#B3446C', '#E3CCDC', '#CDE0E1', '#F0EFED', '#667eea', '#764ba2']

                wedges, texts, autotexts = ax.pie(
                    top_brands.values,
                    labels=top_brands.index,
                    autopct='%1.1f%%',
                    colors=colors,
                    startangle=90,
                    pctdistance=0.85,
                    textprops={'fontsize': 11, 'weight': 'bold', 'color': '#32127A'}
                )

                # Improve text contrast
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
                    autotext.set_fontsize(10)

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')
                    text.set_fontsize(10)

                # Create donut effect
                centre_circle = plt.Circle((0,0), 0.70, fc='#F0EFED')
                fig.gca().add_artist(centre_circle)

                ax.set_title("Top 8 Brands Market Share", fontsize=16, weight='bold', color='#32127A', pad=20)
                plt.tight_layout()

                # Add container styling
                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)

                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if 'Brand' in data.columns and 'Final Price' in data.columns:
                # Enhanced bar chart with better styling
                brand_price = data.groupby('Brand')['Final Price'].agg(['mean', 'count']).reset_index()
                brand_price = brand_price[brand_price['count'] >= 5].sort_values('mean', ascending=True)

                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')  # Match background

                bars = ax.barh(brand_price['Brand'], brand_price['mean'],
                              color='#008292', alpha=0.8, edgecolor='#32127A', linewidth=1.5)

                # Improve text styling
                ax.set_xlabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Average Price by Brand\n(Brands with 5+ models)', fontsize=14, weight='bold', color='#32127A')
                ax.grid(axis='x', alpha=0.3, color='#32127A')

                # Style the axes
                ax.tick_params(colors='#32127A', labelsize=10)
                ax.spines['bottom'].set_color('#32127A')
                ax.spines['left'].set_color('#32127A')
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)

                # Add value labels with better contrast
                for i, bar in enumerate(bars):
                    width = bar.get_width()
                    ax.text(width + 20, bar.get_y() + bar.get_height()/2,
                           f'${width:.0f}', ha='left', va='center', fontweight='bold', color='#32127A')

                plt.tight_layout()

                # Add container styling
                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)

                st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab2:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">💰 Price Analytics Dashboard</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Deep dive into laptop pricing patterns and market segments</p>
        </div>
        """, unsafe_allow_html=True)

        # Price Overview Metrics
        if 'Final Price' in data.columns:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                avg_price = data['Final Price'].mean()
                st.metric("💵 Average Price", f"${avg_price:.0f}")

            with col2:
                median_price = data['Final Price'].median()
                st.metric("📈 Median Price", f"${median_price:.0f}")

            with col3:
                min_price = data['Final Price'].min()
                max_price = data['Final Price'].max()
                st.metric("� Price Range", f"${min_price:.0f} - ${max_price:.0f}")

            with col4:
                price_std = data['Final Price'].std()
                st.metric("📊 Price Volatility", f"${price_std:.0f}")

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Price Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Final Price' in data.columns:
                # Price distribution histogram
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                n, bins, patches = ax.hist(data['Final Price'], bins=30, alpha=0.8, color='#008292', edgecolor='#32127A')

                ax.set_xlabel('Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Price Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if all(col in data.columns for col in ['RAM', 'Final Price']):
                # RAM vs Price scatter plot
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                scatter = ax.scatter(data['RAM'], data['Final Price'],
                                   c=data['Final Price'], cmap='viridis',
                                   alpha=0.6, s=60, edgecolors='#32127A', linewidth=0.5)

                ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('RAM vs Price Correlation', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.colorbar(scatter, label='Price ($)')
                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # CPU Price Analysis
        if all(col in data.columns for col in ['CPU', 'Final Price']):
            fig, ax = plt.subplots(figsize=(16, 8))
            fig.patch.set_facecolor('#F0EFED')

            sns.barplot(data=data, x='CPU', y='Final Price', estimator='mean', ax=ax, palette='viridis')
            ax.set_title('Average Final Price by CPU Type', fontsize=16, weight='bold', color='#32127A')
            ax.set_xlabel('CPU Type', fontsize=12, weight='bold', color='#32127A')
            ax.set_ylabel('Average Final Price ($)', fontsize=12, weight='bold', color='#32127A')
            ax.tick_params(colors='#32127A', labelsize=9)
            ax.grid(True, alpha=0.3, color='#32127A')

            for spine in ax.spines.values():
                spine.set_color('#32127A')

            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            st.markdown("""
            <div style="
                background: rgba(255, 255, 255, 0.9);
                padding: 1rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                border-left: 4px solid #32127A;
                margin-bottom: 1rem;
            ">
            """, unsafe_allow_html=True)

            st.pyplot(fig)
            st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab3:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">💾 Hardware Specifications Analysis</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive analysis of laptop hardware components and performance specs</p>
        </div>
        """, unsafe_allow_html=True)

        # Hardware Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if 'RAM' in data.columns:
                avg_ram = data['RAM'].mean()
                st.metric("🧠 Average RAM", f"{avg_ram:.1f} GB")

        with col2:
            if 'Storage' in data.columns:
                avg_storage = data['Storage'].mean()
                st.metric("💾 Average Storage", f"{avg_storage:.0f} GB")

        with col3:
            if 'CPU' in data.columns:
                unique_cpus = data['CPU'].nunique()
                st.metric("💻 CPU Types", unique_cpus)

        with col4:
            if 'GPU' in data.columns:
                gpu_count = data['GPU'].notna().sum()
                st.metric("🎮 Dedicated GPUs", gpu_count)

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Hardware Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'RAM' in data.columns:
                # RAM distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                ram_counts = data['RAM'].value_counts().sort_index()
                bars = ax.bar(ram_counts.index, ram_counts.values, color='#008292', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('RAM (GB)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('RAM Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels on bars
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if 'Storage' in data.columns:
                # Storage distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                # Create storage bins
                storage_bins = [0, 256, 512, 1000, 2000, float('inf')]
                storage_labels = ['≤256GB', '257-512GB', '513GB-1TB', '1-2TB', '>2TB']
                data['storage_category'] = pd.cut(data['Storage'], bins=storage_bins, labels=storage_labels, right=False)

                storage_counts = data['storage_category'].value_counts()
                colors = ['#CDE0E1', '#008292', '#B3446C', '#32127A', '#E3CCDC']

                bars = ax.bar(range(len(storage_counts)), storage_counts.values,
                             color=colors[:len(storage_counts)], alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Storage Category', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Storage Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.set_xticks(range(len(storage_counts)))
                ax.set_xticklabels(storage_counts.index, rotation=45)
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # CPU Analysis
        if 'CPU' in data.columns:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">💻 CPU Performance Analysis</h4>
            </div>
            """, unsafe_allow_html=True)

            # CPU brand distribution
            cpu_brands = data['CPU'].str.extract(r'(Intel|AMD)')[0].value_counts()

            if not cpu_brands.empty:
                fig, ax = plt.subplots(figsize=(8, 6))
                fig.patch.set_facecolor('#F0EFED')

                colors = ['#32127A', '#008292']
                wedges, texts, autotexts = ax.pie(cpu_brands.values, labels=cpu_brands.index,
                                                 autopct='%1.1f%%', colors=colors, startangle=90)

                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')

                ax.set_title('CPU Brand Distribution', fontsize=14, weight='bold', color='#32127A')

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #32127A;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # Numerical Feature Distributions (Histograms)
        st.markdown("""
        <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
            <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">📈 Hardware Feature Distributions</h4>
        </div>
        """, unsafe_allow_html=True)

        num_features = data.select_dtypes(include=['int', 'float']).columns
        if len(num_features) > 0:
            for i in range(0, len(num_features), 2):
                cols = st.columns(2)
                for j in range(2):
                    if i+j < len(num_features):
                        with cols[j]:
                            fig, ax = plt.subplots(figsize=(10, 6))
                            fig.patch.set_facecolor('#F0EFED')

                            # Enhanced histogram with KDE
                            feature_name = num_features[i+j]
                            ax.hist(data[feature_name].dropna(), bins=20, alpha=0.7, color='#008292', edgecolor='#32127A')

                            # Add KDE line if seaborn is available
                            try:
                                ax2 = ax.twinx()
                                data[feature_name].dropna().plot.kde(ax=ax2, color='#B3446C', linewidth=2)
                                ax2.set_ylabel('Density', color='#32127A', fontweight='bold')
                                ax2.tick_params(colors='#32127A')
                                ax2.spines['right'].set_color('#32127A')
                            except:
                                pass

                            ax.set_xlabel(feature_name, fontsize=12, weight='bold', color='#32127A')
                            ax.set_ylabel('Frequency', fontsize=12, weight='bold', color='#32127A')
                            ax.set_title(f'Distribution of {feature_name}', fontsize=14, weight='bold', color='#32127A')
                            ax.grid(True, alpha=0.3, color='#32127A')
                            ax.tick_params(colors='#32127A')

                            for spine in ax.spines.values():
                                spine.set_color('#32127A')

                            plt.tight_layout()

                            st.markdown("""
                            <div style="
                                background: rgba(255, 255, 255, 0.9);
                                padding: 1rem;
                                border-radius: 15px;
                                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                                border-left: 4px solid #32127A;
                                margin-bottom: 1rem;
                            ">
                            """, unsafe_allow_html=True)

                            st.pyplot(fig)
                            st.markdown("</div>", unsafe_allow_html=True)
        else:
            st.warning("⚠️ No numerical features found in data")

    with viz_tab4:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">�️ Display & Design Features</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Analysis of screen specifications and design characteristics</p>
        </div>
        """, unsafe_allow_html=True)

        # Display Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if 'Screen' in data.columns:
                avg_screen = data['Screen'].mean()
                st.metric("🖥️ Average Screen", f"{avg_screen:.1f}\"")

        with col2:
            if 'Touch' in data.columns:
                touch_count = data['Touch'].sum() if data['Touch'].dtype in ['int64', 'float64'] else 0
                touch_pct = (touch_count / len(data)) * 100
                st.metric("🔍 Touchscreen", f"{touch_pct:.1f}%")

        with col3:
            if 'Screen' in data.columns:
                screen_sizes = data['Screen'].nunique()
                st.metric("📏 Screen Sizes", screen_sizes)

        with col4:
            if 'Brand' in data.columns:
                design_variety = data['Brand'].nunique()
                st.metric("🎨 Design Variety", f"{design_variety} brands")

        st.markdown("<br>", unsafe_allow_html=True)

        # Enhanced Display Visualizations
        col1, col2 = st.columns(2)

        with col1:
            if 'Screen' in data.columns:
                # Screen size distribution
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                screen_counts = data['Screen'].value_counts().sort_index()
                bars = ax.bar(screen_counts.index, screen_counts.values, color='#008292', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Screen Size (inches)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Screen Size Distribution', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                           f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        with col2:
            if all(col in data.columns for col in ['Screen', 'Final Price']):
                # Screen size vs Price analysis
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                # Group by screen size and calculate average price
                screen_price = data.groupby('Screen')['Final Price'].mean().sort_index()

                bars = ax.bar(screen_price.index, screen_price.values, color='#B3446C', alpha=0.8, edgecolor='#32127A')

                ax.set_xlabel('Screen Size (inches)', fontsize=12, weight='bold', color='#32127A')
                ax.set_ylabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                ax.set_title('Average Price by Screen Size', fontsize=14, weight='bold', color='#32127A')
                ax.grid(True, alpha=0.3, color='#32127A')
                ax.tick_params(colors='#32127A')

                # Add value labels
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                           f'${height:.0f}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                for spine in ax.spines.values():
                    spine.set_color('#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #B3446C;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

        # Additional Display Analysis
        if 'Touch' in data.columns:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.5rem; border-radius: 15px; margin: 2rem 0;">
                <h4 style="color: #32127A; margin-bottom: 1rem; font-weight: 700;">🔍 Touchscreen Technology Analysis</h4>
            </div>
            """, unsafe_allow_html=True)

            col1, col2 = st.columns(2)

            with col1:
                # Touchscreen vs Non-touchscreen pie chart
                fig, ax = plt.subplots(figsize=(10, 8))
                fig.patch.set_facecolor('#F0EFED')

                touch_counts = data['Touch'].value_counts()
                labels = ['Non-Touchscreen', 'Touchscreen']
                colors = ['#CDE0E1', '#32127A']

                wedges, texts, autotexts = ax.pie(touch_counts.values, labels=labels,
                                                 autopct='%1.1f%%', colors=colors, startangle=90)

                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

                for text in texts:
                    text.set_color('#32127A')
                    text.set_fontweight('bold')

                ax.set_title('Touchscreen vs Non-Touchscreen Distribution', fontsize=14, weight='bold', color='#32127A')

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

            with col2:
                if all(col in data.columns for col in ['Touch', 'Final Price']):
                    # Touchscreen impact on price
                    fig, ax = plt.subplots(figsize=(10, 8))
                    fig.patch.set_facecolor('#F0EFED')

                    touch_price = data.groupby('Touch')['Final Price'].mean()
                    labels = ['Non-Touchscreen', 'Touchscreen']
                    colors = ['#CDE0E1', '#32127A']

                    bars = ax.bar(labels, touch_price.values, color=colors, alpha=0.8, edgecolor='#32127A')

                    ax.set_ylabel('Average Price ($)', fontsize=12, weight='bold', color='#32127A')
                    ax.set_title('Price Impact of Touchscreen Technology', fontsize=14, weight='bold', color='#32127A')
                    ax.grid(True, alpha=0.3, color='#32127A')
                    ax.tick_params(colors='#32127A')

                    # Add value labels
                    for bar in bars:
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                               f'${height:.0f}', ha='center', va='bottom', fontweight='bold', color='#32127A')

                    for spine in ax.spines.values():
                        spine.set_color('#32127A')

                    plt.tight_layout()

                    st.markdown("""
                    <div style="
                        background: rgba(255, 255, 255, 0.9);
                        padding: 1rem;
                        border-radius: 15px;
                        box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                        border-left: 4px solid #B3446C;
                        margin-bottom: 1rem;
                    ">
                    """, unsafe_allow_html=True)

                    st.pyplot(fig)
                    st.markdown("</div>", unsafe_allow_html=True)

    with viz_tab5:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">🔗 Feature Correlations</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Explore relationships and correlations between different laptop features</p>
        </div>
        """, unsafe_allow_html=True)

        # Correlation Analysis
        numeric_data = data.select_dtypes(include=['number'])
        if len(numeric_data.columns) > 1:
            selected_corr_cols = st.multiselect(
                "Select columns for correlation matrix:",
                numeric_data.columns.tolist(),
                default=numeric_data.columns.tolist()[:4]
            )

            if len(selected_corr_cols) >= 2:
                fig, ax = plt.subplots(figsize=(12, 8))
                fig.patch.set_facecolor('#F0EFED')

                corr = numeric_data[selected_corr_cols].corr()
                sns.heatmap(corr, annot=True, square=True, cmap='RdYlBu_r', center=0, ax=ax)

                ax.set_title('Feature Correlation Matrix', fontsize=14, weight='bold', color='#32127A')
                ax.tick_params(colors='#32127A')

                plt.tight_layout()

                st.markdown("""
                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 1rem;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                    border-left: 4px solid #008292;
                    margin-bottom: 1rem;
                ">
                """, unsafe_allow_html=True)

                st.pyplot(fig)
                st.markdown("</div>", unsafe_allow_html=True)

                # Add Pair Plot Section
                st.markdown("<br>", unsafe_allow_html=True)
                st.markdown("""
                <div style="background: linear-gradient(135deg, #CDE0E1 0%, #E3CCDC 100%); padding: 1.2rem; border-radius: 12px; margin: 1.5rem 0;">
                    <h4 style="color: #32127A; margin-bottom: 0.8rem; font-weight: 700;">🔗 Pair Plot Analysis</h4>
                    <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive pairwise relationships between numerical features</p>
                </div>
                """, unsafe_allow_html=True)

                # Select features for pair plot
                pair_plot_cols = st.multiselect(
                    "Select features for pair plot (max 5 for performance):",
                    selected_corr_cols,
                    default=selected_corr_cols[:4] if len(selected_corr_cols) >= 4 else selected_corr_cols
                )

                if len(pair_plot_cols) >= 2 and len(pair_plot_cols) <= 5:
                    try:
                        # Create pair plot
                        fig, axes = plt.subplots(len(pair_plot_cols), len(pair_plot_cols),
                                               figsize=(15, 15))
                        fig.patch.set_facecolor('#F0EFED')

                        for i, col1 in enumerate(pair_plot_cols):
                            for j, col2 in enumerate(pair_plot_cols):
                                ax = axes[i, j] if len(pair_plot_cols) > 1 else axes

                                if i == j:
                                    # Diagonal: histogram
                                    ax.hist(numeric_data[col1].dropna(), bins=20,
                                           alpha=0.7, color='#008292', edgecolor='#32127A')
                                    ax.set_title(f'{col1} Distribution', fontsize=10,
                                               weight='bold', color='#32127A')
                                else:
                                    # Off-diagonal: scatter plot
                                    ax.scatter(numeric_data[col2], numeric_data[col1],
                                             alpha=0.6, s=30, color='#B3446C',
                                             edgecolors='#32127A', linewidth=0.5)

                                    # Add correlation coefficient
                                    corr_coef = numeric_data[col1].corr(numeric_data[col2])
                                    ax.text(0.05, 0.95, f'r={corr_coef:.2f}',
                                           transform=ax.transAxes, fontsize=9,
                                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                                           color='#32127A', weight='bold')

                                # Styling
                                ax.tick_params(colors='#32127A', labelsize=8)
                                ax.grid(True, alpha=0.3, color='#32127A')

                                for spine in ax.spines.values():
                                    spine.set_color('#32127A')

                                if i == len(pair_plot_cols) - 1:
                                    ax.set_xlabel(col2, fontsize=10, weight='bold', color='#32127A')
                                if j == 0:
                                    ax.set_ylabel(col1, fontsize=10, weight='bold', color='#32127A')

                        plt.suptitle('Feature Pair Plot Analysis', fontsize=16,
                                   weight='bold', color='#32127A', y=0.98)
                        plt.tight_layout()

                        st.markdown("""
                        <div style="
                            background: rgba(255, 255, 255, 0.9);
                            padding: 1rem;
                            border-radius: 15px;
                            box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                            border-left: 4px solid #B3446C;
                            margin-bottom: 1rem;
                        ">
                        """, unsafe_allow_html=True)

                        st.pyplot(fig)
                        st.markdown("</div>", unsafe_allow_html=True)

                        # Add interpretation
                        st.markdown("""
                        <div style="background: rgba(179, 68, 108, 0.1); padding: 1rem; border-radius: 10px; margin-top: 1rem;">
                            <h5 style="color: #32127A; margin-bottom: 0.5rem;">📊 Pair Plot Interpretation:</h5>
                            <ul style="color: #32127A; margin: 0;">
                                <li><strong>Diagonal plots:</strong> Show the distribution of each feature</li>
                                <li><strong>Scatter plots:</strong> Show relationships between feature pairs</li>
                                <li><strong>Correlation values (r):</strong> Range from -1 to 1, closer to ±1 indicates stronger relationship</li>
                                <li><strong>Patterns:</strong> Look for linear trends, clusters, or outliers</li>
                            </ul>
                        </div>
                        """, unsafe_allow_html=True)

                    except Exception as e:
                        st.error(f"❌ Error generating pair plot: {str(e)}")
                        st.info("💡 Try selecting fewer features or check for missing values.")

                elif len(pair_plot_cols) > 5:
                    st.warning("⚠️ Please select maximum 5 features for optimal performance.")
                elif len(pair_plot_cols) < 2:
                    st.info("ℹ️ Please select at least 2 features for pair plot analysis.")
            else:
                st.info("ℹ️ Please select at least two columns for correlation matrix.")
        else:
            st.warning("⚠️ Not enough numerical features for correlation matrix")

    with viz_tab6:
        st.markdown("""
        <div style="background: linear-gradient(135deg, #E3CCDC 0%, #CDE0E1 100%); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
            <h3 style="color: #32127A; margin-bottom: 0.5rem; font-weight: 700;">📈 Market Overview</h3>
            <p style="color: #32127A; opacity: 0.8; margin: 0;">Comprehensive market analysis and trends in the laptop industry</p>
        </div>
        """, unsafe_allow_html=True)

        # Market Overview Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_models = len(data)
            st.metric("💻 Total Models", total_models)

        with col2:
            if 'Final Price' in data.columns:
                market_value = data['Final Price'].sum()
                st.metric("💰 Market Value", f"${market_value/1000000:.1f}M")

        with col3:
            if 'Brand' in data.columns:
                market_leaders = data['Brand'].nunique()
                st.metric("🏆 Market Players", market_leaders)

        with col4:
            if 'Final Price' in data.columns:
                avg_market_price = data['Final Price'].mean()
                st.metric("📈 Avg Market Price", f"${avg_market_price:.0f}")

        st.markdown("<br>", unsafe_allow_html=True)

        # Market Segmentation
        if 'Final Price' in data.columns:
            fig, ax = plt.subplots(figsize=(12, 6))
            fig.patch.set_facecolor('#F0EFED')

            # Define market segments
            budget = len(data[data['Final Price'] < 500])
            mid_range = len(data[(data['Final Price'] >= 500) & (data['Final Price'] < 1000)])
            premium = len(data[(data['Final Price'] >= 1000) & (data['Final Price'] < 1500)])
            luxury = len(data[data['Final Price'] >= 1500])

            segments = ['Budget\n(<$500)', 'Mid-Range\n($500-$999)', 'Premium\n($1000-$1499)', 'Luxury\n($1500+)']
            sizes = [budget, mid_range, premium, luxury]
            colors = ['#CDE0E1', '#008292', '#B3446C', '#32127A']

            bars = ax.bar(segments, sizes, color=colors, alpha=0.8, edgecolor='#32127A')

            ax.set_xlabel('Market Segment', fontsize=12, weight='bold', color='#32127A')
            ax.set_ylabel('Number of Laptops', fontsize=12, weight='bold', color='#32127A')
            ax.set_title('Market Segmentation by Price Range', fontsize=14, weight='bold', color='#32127A')
            ax.grid(True, alpha=0.3, color='#32127A')
            ax.tick_params(colors='#32127A')

            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                       f'{int(height)}', ha='center', va='bottom', fontweight='bold', color='#32127A')

            for spine in ax.spines.values():
                spine.set_color('#32127A')

            plt.tight_layout()

            st.markdown("""
            <div style="
                background: rgba(255, 255, 255, 0.9);
                padding: 1rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(50, 18, 122, 0.1);
                border-left: 4px solid #B3446C;
                margin-bottom: 1rem;
            ">
            """, unsafe_allow_html=True)

            st.pyplot(fig)
            st.markdown("</div>", unsafe_allow_html=True)

with tab3:
    # Enhanced header with modern styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #32127A 0%, #008292 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 25px rgba(50, 18, 122, 0.3);
    ">
        <h1 style="font-size: 2.5rem; margin-bottom: 0.5rem; font-weight: 700;">🧹 Interactive Data Cleaning</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Step-by-step data preprocessing with full user control</p>
    </div>
    """, unsafe_allow_html=True)


with tab4:
    st.header("🤖 Model Evaluation")
    st.write("""
    🏁 Select the target column and model, then view performance results and the ROC curve.
    """)


with tab5:
    st.header("💻 Laptop Recommendation")
    st.write("""
    🏁 Select your preferences and get personalized laptop recommendations.
    """)
